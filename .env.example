# Clove - Claude API AI Configuration Example
# Copy this file to .env and modify the values as needed

# =============================================================================
# Server Settings
# =============================================================================

# Server host address (default: 0.0.0.0)
HOST=0.0.0.0

# Server port (default: 5201)
PORT=5201

# =============================================================================
# Application Configuration
# =============================================================================

# Folder path for storing persistent data (accounts, etc.)
#DATA_FOLDER=~/.clove/data

# Folder path for storing translation files
#LOCALES_FOLDER=

# Folder path for storing static files
#STATIC_FOLDER=

# Default language code for translations
#DEFAULT_LANGUAGE=en

# Number of retry attempts for failed requests (default: 3)
#RETRY_ATTEMPTS=3

# Interval between retry attempts in seconds (default: 1)
#RETRY_INTERVAL=1

# =============================================================================
# Proxy Settings
# =============================================================================

# HTTP/HTTPS proxy URL (optional)
# PROXY_URL=http://proxy.example.com:8080

# =============================================================================
# API Keys
# =============================================================================

# Comma-separated list of API keys for authentication
# API_KEYS=your-api-key-1,your-api-key-2

# Comma-separated list of admin API keys for management
# ADMIN_API_KEYS=your-admin-api-key-1,your-admin-api-key-2

# =============================================================================
# Claude URLs
# =============================================================================

# Claude AI web interface URL
#CLAUDE_AI_URL=https://claude.ai

# Claude API base URL
#CLAUDE_API_BASEURL=https://api.anthropic.com

# =============================================================================
# Cookies
# =============================================================================

# Comma-separated list of Claude.ai cookies for web interface access
# COOKIES=sessionKey=your-session-key,sessionKey=your-session-key-2

# =============================================================================
# Content Processing
# =============================================================================

# Custom prompt to prepend to all requests (optional)
# CUSTOM_PROMPT=You are a helpful assistant.

# Use real role names in conversation (default: true)
#USE_REAL_ROLES=true

# Custom name for human role (default: Human)
#CUSTOM_HUMAN_NAME=Human

# Custom name for assistant role (default: Assistant)
#CUSTOM_ASSISTANT_NAME=Assistant

# Padding tokens for content processing (comma-separated)
# PAD_TOKENS=token1,token2

# Padding text length (default: 0)
#PADTXT_LENGTH=0

# Allow downloading images from external URLs (default: false)
#ALLOW_EXTERNAL_IMAGES=false

# =============================================================================
# Request Settings
# =============================================================================

# Request timeout in seconds (default: 60)
#REQUEST_TIMEOUT=60

# Number of retries for failed requests (default: 3)
#REQUEST_RETRIES=3

# Interval between retries in seconds (default: 1)
#REQUEST_RETRY_INTERVAL=1

# =============================================================================
# Feature Flags
# =============================================================================

# Preserve chat history (default: false)
#PRESERVE_CHATS=false

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
#LOG_LEVEL=INFO

# Enable logging to file (default: false)
#LOG_TO_FILE=false

# Log file path (default: logs/app.log)
#LOG_FILE_PATH=logs/app.log

# Log file rotation (e.g., '10 MB', '1 day', '1 week')
#LOG_FILE_ROTATION=10 MB

# Log file retention (e.g., '7 days', '1 month')
#LOG_FILE_RETENTION=7 days

# Log file compression format (default: zip)
#LOG_FILE_COMPRESSION=zip

# =============================================================================
# Session Management
# =============================================================================

# Session idle timeout in seconds (default: 300)
#SESSION_TIMEOUT=300

# Interval for cleaning up expired sessions in seconds (default: 30)
#SESSION_CLEANUP_INTERVAL=30

# Maximum number of concurrent sessions per cookie (default: 3)
#MAX_SESSIONS_PER_COOKIE=3

# =============================================================================
# Account Management
# =============================================================================

# Interval for account management task in seconds (default: 60)
#ACCOUNT_TASK_INTERVAL=60

# =============================================================================
# Tool Call Settings
# =============================================================================

# Timeout for pending tool calls in seconds (default: 300)
#TOOL_CALL_TIMEOUT=300

# Interval for cleaning up expired tool calls in seconds (default: 60)
#TOOL_CALL_CLEANUP_INTERVAL=60

# =============================================================================
# Claude OAuth Settings
# =============================================================================

# OAuth client ID for Claude authentication
#OAUTH_CLIENT_ID=9d1c250a-e61b-44d9-88ed-5944d1962f5e

# OAuth authorization endpoint URL template
#OAUTH_AUTHORIZE_URL=https://claude.ai/v1/oauth/{organization_uuid}/authorize

# OAuth token exchange endpoint URL
#OAUTH_TOKEN_URL=https://console.anthropic.com/v1/oauth/token

# OAuth redirect URI for authorization flow
#OAUTH_REDIRECT_URI=https://console.anthropic.com/oauth/code/callback
