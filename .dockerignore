# Git
.git/
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
.coverage.*
.cache
htmlcov/
.tox/
.nox/
*.cover
*.py,cover
.hypothesis/
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
.ruff_cache/
uv.lock

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.envrc

# Node.js / Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/
lerna-debug.log*
.npm
.eslintcache
.stylelintcache
.node_repl_history
*.tsbuildinfo
.yarn/
.pnp.*

# Frontend build outputs (will be built in Docker)
front/dist/
front/build/
app/static/assets/
app/static/index.html
app/static/vite.svg

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
*.sublime-project
*.sublime-workspace
.project
.classpath
.c9/
*.launch
.settings/
*.iml
.cursorignore
.cursorindexingignore

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini

# Documentation
docs/
*.md
!README.md
LICENSE

# Testing
coverage/
.nyc_output/
test/
tests/
*.test.js
*.spec.js
__tests__/

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Temporary files
*.tmp
*.temp
.tmp/
.temp/
tmp/
temp/

# Local data (contains sensitive information)
data/
/data/
*.json
!package.json
!tsconfig*.json
!components.json
!app/locales/*.json

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Makefile and scripts
Makefile
scripts/

# Python packaging files
pyproject.toml
requirements*.txt
!requirements.txt
MANIFEST.in
setup.py
setup.cfg

# Miscellaneous
*.bak
*.orig
*.rej
.cache/