name: Build and Publish to PyPI

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      publish_to_pypi:
        description: "Publish to PyPI"
        required: true
        default: false
        type: boolean
      publish_to_test_pypi:
        description: "Publish to Test PyPI"
        required: true
        default: false
        type: boolean

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build wheel

      - name: Build frontend and wheel
        run: |
          python scripts/build_wheel.py

      - name: Store the distribution packages
        uses: actions/upload-artifact@v4
        with:
          name: python-package-distributions
          path: dist/

  publish-to-pypi:
    name: Publish to PyPI
    if: github.event_name == 'release' || (github.event_name == 'workflow_dispatch' && github.event.inputs.publish_to_pypi == 'true')
    needs:
      - build
    runs-on: ubuntu-latest

    environment:
      name: pypi
      url: https://pypi.org/p/clove-proxy

    permissions:
      id-token: write

    steps:
      - name: Download all the dists
        uses: actions/download-artifact@v4
        with:
          name: python-package-distributions
          path: dist/

      - name: Publish distribution to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}

  publish-to-testpypi:
    name: Publish to TestPyPI
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.publish_to_test_pypi == 'true'
    needs:
      - build
    runs-on: ubuntu-latest

    environment:
      name: testpypi
      url: https://test.pypi.org/p/clove-proxy

    permissions:
      id-token: write

    steps:
      - name: Download all the dists
        uses: actions/download-artifact@v4
        with:
          name: python-package-distributions
          path: dist/

      - name: Publish distribution to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          repository-url: https://test.pypi.org/legacy/
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
